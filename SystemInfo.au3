#include <GUIConstantsEx.au3>
#include <TabConstants.au3>
#include <ListViewConstants.au3>
#include <WindowsConstants.au3>
#include <Array.au3>
#include <File.au3>
#include <GuiListView.au3>

; ===============================================================================
; AutoIt System Information Viewer
; 系统信息查看器 - GUI版本
; 作者: AutoIt脚本
; 版本: 1.0
; ===============================================================================

; 全局变量声明
Global $hMainGUI, $hTabControl
Global $hList_System, $hList_CPU, $hList_Memory, $hList_Storage, $hList_Network, $hList_Software
Global $hBtn_Refresh, $hBtn_Export, $hBtn_About
Global $aSystemInfo[0][2], $aCPUInfo[0][2], $aMemoryInfo[0][2]
Global $aStorageInfo[0][2], $aNetworkInfo[0][2], $aSoftwareInfo[0][2]

; 主程序入口
Main()

; ===============================================================================
; 主函数
; ===============================================================================
Func Main()
    ; 创建主窗口
    CreateMainGUI()
    
    ; 收集系统信息
    CollectAllSystemInfo()
    
    ; 显示窗口
    GUISetState(@SW_SHOW, $hMainGUI)
    
    ; 消息循环
    While 1
        $nMsg = GUIGetMsg()
        Switch $nMsg
            Case $GUI_EVENT_CLOSE
                ExitLoop
            Case $hBtn_Refresh
                RefreshAllInfo()
            Case $hBtn_Export
                ExportToText()
            Case $hBtn_About
                ShowAboutDialog()
        EndSwitch
    WEnd
    
    ; 清理并退出
    GUIDelete($hMainGUI)
EndFunc

; ===============================================================================
; 创建主GUI界面
; ===============================================================================
Func CreateMainGUI()
    ; 创建主窗口 800x600 居中显示
    $hMainGUI = GUICreate("系统信息查看器 v1.0", 800, 600, -1, -1, $WS_OVERLAPPEDWINDOW)
    
    ; 创建标签页控件
    $hTabControl = GUICtrlCreateTab(10, 10, 780, 520)
    
    ; 创建各个标签页
    CreateSystemTab()
    CreateCPUTab()
    CreateMemoryTab()
    CreateStorageTab()
    CreateNetworkTab()
    CreateSoftwareTab()
    
    ; 创建操作按钮
    $hBtn_Refresh = GUICtrlCreateButton("刷新信息", 50, 550, 100, 30)
    $hBtn_Export = GUICtrlCreateButton("导出报告", 200, 550, 100, 30)
    $hBtn_About = GUICtrlCreateButton("关于", 350, 550, 100, 30)
    
    ; 设置默认选中第一个标签页
    GUICtrlSetState($hTabControl, $GUI_SHOW)
EndFunc

; ===============================================================================
; 创建系统概览标签页
; ===============================================================================
Func CreateSystemTab()
    GUICtrlCreateTabItem("系统概览")
    $hList_System = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_System, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_System, 1, 480)
EndFunc

; ===============================================================================
; 创建处理器标签页
; ===============================================================================
Func CreateCPUTab()
    GUICtrlCreateTabItem("处理器")
    $hList_CPU = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_CPU, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_CPU, 1, 480)
EndFunc

; ===============================================================================
; 创建内存标签页
; ===============================================================================
Func CreateMemoryTab()
    GUICtrlCreateTabItem("内存")
    $hList_Memory = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_Memory, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_Memory, 1, 480)
EndFunc

; ===============================================================================
; 创建存储标签页
; ===============================================================================
Func CreateStorageTab()
    GUICtrlCreateTabItem("存储")
    $hList_Storage = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_Storage, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_Storage, 1, 480)
EndFunc

; ===============================================================================
; 创建网络标签页
; ===============================================================================
Func CreateNetworkTab()
    GUICtrlCreateTabItem("网络")
    $hList_Network = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_Network, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_Network, 1, 480)
EndFunc

; ===============================================================================
; 创建软件标签页
; ===============================================================================
Func CreateSoftwareTab()
    GUICtrlCreateTabItem("软件")
    $hList_Software = GUICtrlCreateListView("属性|值", 20, 40, 750, 470, $LVS_REPORT)
    _GUICtrlListView_SetColumnWidth($hList_Software, 0, 250)
    _GUICtrlListView_SetColumnWidth($hList_Software, 1, 480)
    GUICtrlCreateTabItem("")  ; 结束标签页创建
EndFunc

; ===============================================================================
; 收集所有系统信息
; ===============================================================================
Func CollectAllSystemInfo()
    GetSystemInfo()
    GetHardwareInfo()
    GetStorageInfo()
    GetNetworkInfo()
    GetSoftwareInfo()
    
    ; 填充ListView控件
    PopulateListViews()
EndFunc

; ===============================================================================
; 占位函数 - 将在后续步骤中实现
; ===============================================================================
Func GetSystemInfo()
    ; 清空数组
    ReDim $aSystemInfo[0][2]
    
    ; 收集基本系统信息
    _ArrayAdd($aSystemInfo, "操作系统|" & @OSVersion)
    _ArrayAdd($aSystemInfo, "系统构建号|" & @OSBuild)
    _ArrayAdd($aSystemInfo, "系统类型|" & @OSArch)
    _ArrayAdd($aSystemInfo, "计算机名|" & @ComputerName)
    _ArrayAdd($aSystemInfo, "用户名|" & @UserName)
    _ArrayAdd($aSystemInfo, "系统目录|" & @SystemDir)
    _ArrayAdd($aSystemInfo, "Windows目录|" & @WindowsDir)
    _ArrayAdd($aSystemInfo, "程序文件目录|" & @ProgramFilesDir)
    _ArrayAdd($aSystemInfo, "临时目录|" & @TempDir)
    _ArrayAdd($aSystemInfo, "桌面目录|" & @DesktopDir)
    
    ; 获取系统启动时间
    Local $iUptime = Round((@MSEC / 1000) / 3600, 2)
    _ArrayAdd($aSystemInfo, "系统运行时间|" & $iUptime & " 小时")
    
    ; 获取当前日期时间
    _ArrayAdd($aSystemInfo, "当前时间|" & @YEAR & "-" & @MON & "-" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC)
    
    ; 获取系统语言
    _ArrayAdd($aSystemInfo, "系统语言|" & @OSLang)
    
    ; 获取屏幕分辨率
    _ArrayAdd($aSystemInfo, "屏幕分辨率|" & @DesktopWidth & " x " & @DesktopHeight)
    
    ; 获取颜色深度
    _ArrayAdd($aSystemInfo, "颜色深度|" & @DesktopDepth & " 位")
EndFunc

Func GetHardwareInfo()
    ; 清空数组
    ReDim $aCPUInfo[0][2]
    ReDim $aMemoryInfo[0][2]
    
    ; 获取CPU信息
    Local $objWMIService = ObjGet("winmgmts:")
    If IsObj($objWMIService) Then
        ; 查询处理器信息
        Local $colItems = $objWMIService.ExecQuery("SELECT * FROM Win32_Processor")
        For $objItem In $colItems
            _ArrayAdd($aCPUInfo, "处理器名称|" & $objItem.Name)
            _ArrayAdd($aCPUInfo, "制造商|" & $objItem.Manufacturer)
            _ArrayAdd($aCPUInfo, "架构|" & $objItem.Architecture)
            _ArrayAdd($aCPUInfo, "核心数|" & $objItem.NumberOfCores)
            _ArrayAdd($aCPUInfo, "逻辑处理器数|" & $objItem.NumberOfLogicalProcessors)
            _ArrayAdd($aCPUInfo, "最大时钟频率|" & $objItem.MaxClockSpeed & " MHz")
            _ArrayAdd($aCPUInfo, "当前时钟频率|" & $objItem.CurrentClockSpeed & " MHz")
            _ArrayAdd($aCPUInfo, "处理器ID|" & $objItem.ProcessorId)
            _ArrayAdd($aCPUInfo, "套接字类型|" & $objItem.SocketDesignation)
            _ArrayAdd($aCPUInfo, "L2缓存大小|" & $objItem.L2CacheSize & " KB")
            _ArrayAdd($aCPUInfo, "L3缓存大小|" & $objItem.L3CacheSize & " KB")
        Next
        
        ; 查询内存信息
        Local $colMemory = $objWMIService.ExecQuery("SELECT * FROM Win32_PhysicalMemory")
        Local $iTotalMemory = 0, $iMemoryCount = 0
        For $objMemory In $colMemory
            $iMemoryCount += 1
            Local $iCapacity = Round($objMemory.Capacity / 1024 / 1024 / 1024, 2)
            $iTotalMemory += $iCapacity
            _ArrayAdd($aMemoryInfo, "内存条 " & $iMemoryCount & " 容量|" & $iCapacity & " GB")
            _ArrayAdd($aMemoryInfo, "内存条 " & $iMemoryCount & " 速度|" & $objMemory.Speed & " MHz")
            _ArrayAdd($aMemoryInfo, "内存条 " & $iMemoryCount & " 制造商|" & $objMemory.Manufacturer)
            _ArrayAdd($aMemoryInfo, "内存条 " & $iMemoryCount & " 序列号|" & $objMemory.SerialNumber)
        Next
        
        ; 添加总内存信息
        _ArrayAdd($aMemoryInfo, "总物理内存|" & $iTotalMemory & " GB")
        
        ; 获取可用内存
        Local $colOS = $objWMIService.ExecQuery("SELECT * FROM Win32_OperatingSystem")
        For $objOS In $colOS
            Local $iFreeMemory = Round($objOS.FreePhysicalMemory / 1024 / 1024, 2)
            Local $iTotalMemoryOS = Round($objOS.TotalVisibleMemorySize / 1024 / 1024, 2)
            _ArrayAdd($aMemoryInfo, "可用物理内存|" & $iFreeMemory & " GB")
            _ArrayAdd($aMemoryInfo, "内存使用率|" & Round(($iTotalMemoryOS - $iFreeMemory) / $iTotalMemoryOS * 100, 1) & "%")
        Next
    Else
        _ArrayAdd($aCPUInfo, "错误|无法连接到WMI服务")
        _ArrayAdd($aMemoryInfo, "错误|无法连接到WMI服务")
    EndIf
EndFunc

Func GetStorageInfo()
    ; 清空数组
    ReDim $aStorageInfo[0][2]
    
    ; 获取存储设备信息
    Local $objWMIService = ObjGet("winmgmts:")
    If IsObj($objWMIService) Then
        ; 查询逻辑磁盘信息
        Local $colDisks = $objWMIService.ExecQuery("SELECT * FROM Win32_LogicalDisk")
        For $objDisk In $colDisks
            Local $sDriveLetter = $objDisk.DeviceID
            Local $sDriveType = ""
            Switch $objDisk.DriveType
                Case 1
                    $sDriveType = "软盘驱动器"
                Case 2
                    $sDriveType = "可移动磁盘"
                Case 3
                    $sDriveType = "本地硬盘"
                Case 4
                    $sDriveType = "网络驱动器"
                Case 5
                    $sDriveType = "光盘驱动器"
                Case 6
                    $sDriveType = "RAM磁盘"
                Case Else
                    $sDriveType = "未知类型"
            EndSwitch
            
            _ArrayAdd($aStorageInfo, $sDriveLetter & " 驱动器类型|" & $sDriveType)
            _ArrayAdd($aStorageInfo, $sDriveLetter & " 文件系统|" & $objDisk.FileSystem)
            
            If $objDisk.Size <> "" Then
                Local $iTotalSize = Round($objDisk.Size / 1024 / 1024 / 1024, 2)
                Local $iFreeSpace = Round($objDisk.FreeSpace / 1024 / 1024 / 1024, 2)
                Local $iUsedSpace = $iTotalSize - $iFreeSpace
                Local $iUsagePercent = Round($iUsedSpace / $iTotalSize * 100, 1)
                
                _ArrayAdd($aStorageInfo, $sDriveLetter & " 总容量|" & $iTotalSize & " GB")
                _ArrayAdd($aStorageInfo, $sDriveLetter & " 可用空间|" & $iFreeSpace & " GB")
                _ArrayAdd($aStorageInfo, $sDriveLetter & " 已用空间|" & $iUsedSpace & " GB")
                _ArrayAdd($aStorageInfo, $sDriveLetter & " 使用率|" & $iUsagePercent & "%")
            EndIf
            
            If $objDisk.VolumeName <> "" Then
                _ArrayAdd($aStorageInfo, $sDriveLetter & " 卷标|" & $objDisk.VolumeName)
            EndIf
            
            _ArrayAdd($aStorageInfo, "---|---")  ; 分隔线
        Next
        
        ; 查询物理磁盘信息
        Local $colPhysicalDisks = $objWMIService.ExecQuery("SELECT * FROM Win32_DiskDrive")
        For $objPhysicalDisk In $colPhysicalDisks
            _ArrayAdd($aStorageInfo, "物理磁盘|" & $objPhysicalDisk.Model)
            _ArrayAdd($aStorageInfo, "磁盘大小|" & Round($objPhysicalDisk.Size / 1024 / 1024 / 1024, 2) & " GB")
            _ArrayAdd($aStorageInfo, "接口类型|" & $objPhysicalDisk.InterfaceType)
            _ArrayAdd($aStorageInfo, "分区数量|" & $objPhysicalDisk.Partitions)
            _ArrayAdd($aStorageInfo, "序列号|" & $objPhysicalDisk.SerialNumber)
            _ArrayAdd($aStorageInfo, "---|---")  ; 分隔线
        Next
    Else
        _ArrayAdd($aStorageInfo, "错误|无法连接到WMI服务")
    EndIf
EndFunc

Func GetNetworkInfo()
    ; 清空数组
    ReDim $aNetworkInfo[0][2]
    
    ; 获取网络适配器信息
    Local $objWMIService = ObjGet("winmgmts:")
    If IsObj($objWMIService) Then
        ; 查询网络适配器配置信息
        Local $colNetAdapters = $objWMIService.ExecQuery("SELECT * FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled = True")
        Local $iAdapterCount = 0
        
        For $objAdapter In $colNetAdapters
            $iAdapterCount += 1
            _ArrayAdd($aNetworkInfo, "网络适配器 " & $iAdapterCount & "|" & $objAdapter.Description)
            _ArrayAdd($aNetworkInfo, "MAC地址|" & $objAdapter.MACAddress)
            
            ; 获取IP地址
            If IsArray($objAdapter.IPAddress) Then
                For $i = 0 To UBound($objAdapter.IPAddress) - 1
                    _ArrayAdd($aNetworkInfo, "IP地址 " & ($i + 1) & "|" & $objAdapter.IPAddress($i))
                Next
            EndIf
            
            ; 获取子网掩码
            If IsArray($objAdapter.IPSubnet) Then
                For $i = 0 To UBound($objAdapter.IPSubnet) - 1
                    _ArrayAdd($aNetworkInfo, "子网掩码 " & ($i + 1) & "|" & $objAdapter.IPSubnet($i))
                Next
            EndIf
            
            ; 获取默认网关
            If IsArray($objAdapter.DefaultIPGateway) Then
                For $i = 0 To UBound($objAdapter.DefaultIPGateway) - 1
                    _ArrayAdd($aNetworkInfo, "默认网关 " & ($i + 1) & "|" & $objAdapter.DefaultIPGateway($i))
                Next
            EndIf
            
            ; 获取DNS服务器
            If IsArray($objAdapter.DNSServerSearchOrder) Then
                For $i = 0 To UBound($objAdapter.DNSServerSearchOrder) - 1
                    _ArrayAdd($aNetworkInfo, "DNS服务器 " & ($i + 1) & "|" & $objAdapter.DNSServerSearchOrder($i))
                Next
            EndIf
            
            Local $sDHCPStatus = "否"
            If $objAdapter.DHCPEnabled Then $sDHCPStatus = "是"
            _ArrayAdd($aNetworkInfo, "DHCP启用|" & $sDHCPStatus)
            _ArrayAdd($aNetworkInfo, "域名|" & $objAdapter.DNSDomain)
            _ArrayAdd($aNetworkInfo, "---|---")  ; 分隔线
        Next
        
        ; 查询网络适配器统计信息
        Local $colNetStats = $objWMIService.ExecQuery("SELECT * FROM Win32_PerfRawData_Tcpip_NetworkInterface WHERE Name <> '_Total' AND Name <> 'Loopback'")
        For $objStats In $colNetStats
            _ArrayAdd($aNetworkInfo, "网络接口|" & $objStats.Name)
            _ArrayAdd($aNetworkInfo, "接收字节数|" & $objStats.BytesReceivedPerSec)
            _ArrayAdd($aNetworkInfo, "发送字节数|" & $objStats.BytesSentPerSec)
            _ArrayAdd($aNetworkInfo, "---|---")  ; 分隔线
        Next
        
        ; 获取计算机网络信息
        Local $colComputer = $objWMIService.ExecQuery("SELECT * FROM Win32_ComputerSystem")
        For $objComputer In $colComputer
            _ArrayAdd($aNetworkInfo, "计算机域|" & $objComputer.Domain)
            _ArrayAdd($aNetworkInfo, "工作组|" & $objComputer.Workgroup)
            _ArrayAdd($aNetworkInfo, "域角色|" & $objComputer.DomainRole)
        Next
    Else
        _ArrayAdd($aNetworkInfo, "错误|无法连接到WMI服务")
    EndIf
EndFunc

Func GetSoftwareInfo()
    ; 清空数组
    ReDim $aSoftwareInfo[0][2]
    
    ; 获取软件信息
    Local $objWMIService = ObjGet("winmgmts:")
    If IsObj($objWMIService) Then
        ; 查询已安装的软件（通过注册表）
        _ArrayAdd($aSoftwareInfo, "=== 已安装软件 ===|===")
        
        Local $colSoftware = $objWMIService.ExecQuery("SELECT * FROM Win32_Product")
        Local $iSoftwareCount = 0
        
        For $objSoftware In $colSoftware
            $iSoftwareCount += 1
            If $iSoftwareCount <= 20 Then  ; 限制显示前20个软件，避免界面过于拥挤
                _ArrayAdd($aSoftwareInfo, "软件名称|" & $objSoftware.Name)
                _ArrayAdd($aSoftwareInfo, "版本|" & $objSoftware.Version)
                _ArrayAdd($aSoftwareInfo, "发布商|" & $objSoftware.Vendor)
                _ArrayAdd($aSoftwareInfo, "安装日期|" & $objSoftware.InstallDate)
                _ArrayAdd($aSoftwareInfo, "---|---")  ; 分隔线
            EndIf
        Next
        
        _ArrayAdd($aSoftwareInfo, "软件总数|" & $iSoftwareCount & " 个")
        _ArrayAdd($aSoftwareInfo, "---|---")
        
        ; 查询系统服务
        _ArrayAdd($aSoftwareInfo, "=== 系统服务状态 ===|===")
        Local $colServices = $objWMIService.ExecQuery("SELECT * FROM Win32_Service WHERE StartMode = 'Auto'")
        Local $iRunningServices = 0, $iStoppedServices = 0
        
        For $objService In $colServices
            If $objService.State = "Running" Then
                $iRunningServices += 1
            Else
                $iStoppedServices += 1
            EndIf
        Next
        
        _ArrayAdd($aSoftwareInfo, "自动启动服务总数|" & ($iRunningServices + $iStoppedServices))
        _ArrayAdd($aSoftwareInfo, "正在运行的服务|" & $iRunningServices)
        _ArrayAdd($aSoftwareInfo, "已停止的服务|" & $iStoppedServices)
        _ArrayAdd($aSoftwareInfo, "---|---")
        
        ; 查询启动程序
        _ArrayAdd($aSoftwareInfo, "=== 启动程序 ===|===")
        Local $colStartup = $objWMIService.ExecQuery("SELECT * FROM Win32_StartupCommand")
        Local $iStartupCount = 0
        
        For $objStartup In $colStartup
            $iStartupCount += 1
            If $iStartupCount <= 10 Then  ; 限制显示前10个启动程序
                _ArrayAdd($aSoftwareInfo, "启动程序|" & $objStartup.Name)
                _ArrayAdd($aSoftwareInfo, "命令|" & $objStartup.Command)
                _ArrayAdd($aSoftwareInfo, "位置|" & $objStartup.Location)
                _ArrayAdd($aSoftwareInfo, "---|---")
            EndIf
        Next
        
        _ArrayAdd($aSoftwareInfo, "启动程序总数|" & $iStartupCount & " 个")
        _ArrayAdd($aSoftwareInfo, "---|---")
        
        ; 查询进程信息
        _ArrayAdd($aSoftwareInfo, "=== 进程信息 ===|===")
        Local $colProcesses = $objWMIService.ExecQuery("SELECT * FROM Win32_Process")
        Local $iProcessCount = 0
        Local $iTotalMemoryUsage = 0
        
        For $objProcess In $colProcesses
            $iProcessCount += 1
            If $objProcess.WorkingSetSize <> "" Then
                $iTotalMemoryUsage += $objProcess.WorkingSetSize
            EndIf
        Next
        
        _ArrayAdd($aSoftwareInfo, "运行进程总数|" & $iProcessCount & " 个")
        _ArrayAdd($aSoftwareInfo, "进程总内存使用|" & Round($iTotalMemoryUsage / 1024 / 1024, 2) & " MB")
        
    Else
        _ArrayAdd($aSoftwareInfo, "错误|无法连接到WMI服务")
    EndIf
EndFunc

Func PopulateListViews()
    ; 声明局部变量
    Local $i, $aItem
    
    ; 清空所有ListView
    _GUICtrlListView_DeleteAllItems($hList_System)
    _GUICtrlListView_DeleteAllItems($hList_CPU)
    _GUICtrlListView_DeleteAllItems($hList_Memory)
    _GUICtrlListView_DeleteAllItems($hList_Storage)
    _GUICtrlListView_DeleteAllItems($hList_Network)
    _GUICtrlListView_DeleteAllItems($hList_Software)
    
    ; 填充系统信息
    For $i = 0 To UBound($aSystemInfo) - 1
        $aItem = StringSplit($aSystemInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_System)
        EndIf
    Next
    
    ; 填充CPU信息
    For $i = 0 To UBound($aCPUInfo) - 1
        $aItem = StringSplit($aCPUInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_CPU)
        EndIf
    Next
    
    ; 填充内存信息
    For $i = 0 To UBound($aMemoryInfo) - 1
        $aItem = StringSplit($aMemoryInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_Memory)
        EndIf
    Next
    
    ; 填充存储信息
    For $i = 0 To UBound($aStorageInfo) - 1
        $aItem = StringSplit($aStorageInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_Storage)
        EndIf
    Next
    
    ; 填充网络信息
    For $i = 0 To UBound($aNetworkInfo) - 1
        $aItem = StringSplit($aNetworkInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_Network)
        EndIf
    Next
    
    ; 填充软件信息
    For $i = 0 To UBound($aSoftwareInfo) - 1
        $aItem = StringSplit($aSoftwareInfo[$i], "|")
        If $aItem[0] >= 2 Then
            GUICtrlCreateListViewItem($aItem[1] & "|" & $aItem[2], $hList_Software)
        EndIf
    Next
EndFunc

Func RefreshAllInfo()
    ; 显示刷新提示
    GUICtrlSetData($hBtn_Refresh, "刷新中...")
    GUICtrlSetState($hBtn_Refresh, $GUI_DISABLE)
    
    ; 重新收集所有信息
    CollectAllSystemInfo()
    
    ; 恢复按钮状态
    GUICtrlSetData($hBtn_Refresh, "刷新信息")
    GUICtrlSetState($hBtn_Refresh, $GUI_ENABLE)
    
    ; 显示完成提示
    ToolTip("系统信息已刷新", @DesktopWidth - 150, 50)
    Sleep(2000)
    ToolTip("")
EndFunc

Func ExportToText()
    ; 声明局部变量
    Local $i, $aItem, $hFile, $iResult
    Local $sFileName = "SystemInfo_" & @YEAR & @MON & @MDAY & "_" & @HOUR & @MIN & @SEC & ".txt"
    Local $sFilePath = @DesktopDir & "\" & $sFileName
    
    ; 创建文件内容
    Local $sContent = ""
    $sContent &= "===============================================================================" & @CRLF
    $sContent &= "系统信息报告" & @CRLF
    $sContent &= "生成时间: " & @YEAR & "-" & @MON & "-" & @MDAY & " " & @HOUR & ":" & @MIN & ":" & @SEC & @CRLF
    $sContent &= "===============================================================================" & @CRLF & @CRLF
    
    ; 添加系统概览信息
    $sContent &= "[系统概览]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aSystemInfo) - 1
        $aItem = StringSplit($aSystemInfo[$i], "|")
        If $aItem[0] >= 2 Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        EndIf
    Next
    $sContent &= @CRLF
    
    ; 添加处理器信息
    $sContent &= "[处理器信息]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aCPUInfo) - 1
        $aItem = StringSplit($aCPUInfo[$i], "|")
        If $aItem[0] >= 2 Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        EndIf
    Next
    $sContent &= @CRLF
    
    ; 添加内存信息
    $sContent &= "[内存信息]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aMemoryInfo) - 1
        $aItem = StringSplit($aMemoryInfo[$i], "|")
        If $aItem[0] >= 2 Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        EndIf
    Next
    $sContent &= @CRLF
    
    ; 添加存储信息
    $sContent &= "[存储信息]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aStorageInfo) - 1
        $aItem = StringSplit($aStorageInfo[$i], "|")
        If $aItem[0] >= 2 And $aItem[1] <> "---" Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        ElseIf $aItem[1] = "---" Then
            $sContent &= @CRLF
        EndIf
    Next
    $sContent &= @CRLF
    
    ; 添加网络信息
    $sContent &= "[网络信息]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aNetworkInfo) - 1
        $aItem = StringSplit($aNetworkInfo[$i], "|")
        If $aItem[0] >= 2 And $aItem[1] <> "---" Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        ElseIf $aItem[1] = "---" Then
            $sContent &= @CRLF
        EndIf
    Next
    $sContent &= @CRLF
    
    ; 添加软件信息
    $sContent &= "[软件信息]" & @CRLF
    $sContent &= "----------------------------------------" & @CRLF
    For $i = 0 To UBound($aSoftwareInfo) - 1
        $aItem = StringSplit($aSoftwareInfo[$i], "|")
        If $aItem[0] >= 2 And $aItem[1] <> "---" Then
            $sContent &= $aItem[1] & ": " & $aItem[2] & @CRLF
        ElseIf $aItem[1] = "---" Then
            $sContent &= @CRLF
        EndIf
    Next
    
    $sContent &= @CRLF & "===============================================================================" & @CRLF
    $sContent &= "报告结束" & @CRLF
    $sContent &= "==============================================================================="
    
    ; 写入文件
    $hFile = FileOpen($sFilePath, 2)  ; 写入模式，覆盖现有文件
    If $hFile = -1 Then
        MsgBox(16, "错误", "无法创建导出文件!")
        Return
    EndIf
    
    FileWrite($hFile, $sContent)
    FileClose($hFile)
    
    ; 显示成功消息并询问是否打开文件
    $iResult = MsgBox(36, "导出成功", "系统信息已成功导出到:" & @CRLF & $sFilePath & @CRLF & @CRLF & "是否要打开该文件?")
    If $iResult = 6 Then  ; 用户点击"是"
        ShellExecute($sFilePath)
    EndIf
EndFunc

Func ShowAboutDialog()
    MsgBox(64, "关于", "系统信息查看器 v1.0" & @CRLF & @CRLF & "AutoIt脚本编写" & @CRLF & "用于显示详细的系统信息")
EndFunc